const { DataTypes } = require('sequelize');

const defineFavoritesModel = (sequelize) => {
  const Favorites = sequelize.define('Favorites', {
    FavoriteID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    UserID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    CreatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'favorites',
    timestamps: false,
    indexes: [
      { fields: ['UserID'] },
      { fields: ['ListingID'] },
      { fields: ['CreatedAt'] },
      // Composite unique index to prevent duplicate favorites
      { 
        fields: ['UserID', 'ListingID'], 
        unique: true,
        name: 'unique_user_listing_favorite'
      }
    ],
    hooks: {
      beforeUpdate: (favorite) => {
        favorite.UpdatedAt = new Date();
      }
    }
  });

  // Define associations
  Favorites.associate = (models) => {
    // Belongs to User
    Favorites.belongsTo(models.User, {
      foreignKey: 'UserID',
      as: 'User'
    });

    // Belongs to Listing
    Favorites.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });
  };

  return Favorites;
};

module.exports = defineFavoritesModel;
