const { authHook } = require('../../../hooks/auth.hook');
const favoritesController = require('../controllers/favorites.controller');

const favoritesRoutes = async (fastify, options) => {
  // Add listing to favorites
  fastify.post('/listings/:listingId', {
    preHandler: [authHook],
    schema: {
      description: 'Add listing to user favorites',
      tags: ['favorites'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['listingId'],
        properties: {
          listingId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        201: {
          description: 'Listing added to favorites successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                favoriteId: { type: 'string', format: 'uuid' },
                userId: { type: 'string', format: 'uuid' },
                listingId: { type: 'string', format: 'uuid' },
                createdAt: { type: 'string', format: 'date-time' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.addToFavorites);

  // Remove listing from favorites
  fastify.delete('/listings/:listingId', {
    preHandler: [authHook],
    schema: {
      description: 'Remove listing from user favorites',
      tags: ['favorites'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['listingId'],
        properties: {
          listingId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: {
          description: 'Listing removed from favorites successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.removeFromFavorites);

  // Get user's favorites
  fastify.get('/', {
    preHandler: [authHook],
    schema: {
      description: 'Get current user favorites with pagination',
      tags: ['favorites'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          skipCache: { type: 'boolean', default: false }
        }
      },
      response: {
        200: {
          description: 'User favorites retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  FavoriteID: { type: 'string', format: 'uuid' },
                  UserID: { type: 'string', format: 'uuid' },
                  ListingID: { type: 'string', format: 'uuid' },
                  CreatedAt: { type: 'string', format: 'date-time' },
                  Listing: {
                    type: 'object',
                    properties: {
                      ListingID: { type: 'string', format: 'uuid' },
                      Title: { type: 'string' },
                      Location: { type: 'string' },
                      PricePerDay: { type: 'number' },
                      PricePerWeek: { type: 'number' },
                      PricePerMonth: { type: 'number' },
                      Description: { type: 'string' },
                      Condition: { type: 'string' },
                      IsAvailable: { type: 'boolean' },
                      IsActive: { type: 'boolean' },
                      CreatedAt: { type: 'string', format: 'date-time' }
                    }
                  }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.getUserFavorites);

  // Toggle favorite status
  fastify.put('/listings/:listingId/toggle', {
    preHandler: [authHook],
    schema: {
      description: 'Toggle favorite status for a listing',
      tags: ['favorites'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['listingId'],
        properties: {
          listingId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: {
          description: 'Favorite status toggled successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                listingId: { type: 'string', format: 'uuid' },
                action: { type: 'string', enum: ['added', 'removed'] },
                isFavorited: { type: 'boolean' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.toggleFavorite);

  // Check if listing is favorited by user
  fastify.get('/listings/:listingId/status', {
    preHandler: [authHook],
    schema: {
      description: 'Check if listing is favorited by current user',
      tags: ['favorites'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['listingId'],
        properties: {
          listingId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: {
          description: 'Favorite status retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                listingId: { type: 'string', format: 'uuid' },
                isFavorited: { type: 'boolean' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.isListingFavorited);

  // Get listing favorite count (public endpoint)
  fastify.get('/listings/:listingId/count', {
    schema: {
      description: 'Get favorite count for a listing',
      tags: ['favorites'],
      params: {
        type: 'object',
        required: ['listingId'],
        properties: {
          listingId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: {
          description: 'Listing favorite count retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                listingId: { type: 'string', format: 'uuid' },
                favoriteCount: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.getListingFavoriteCount);

  // Get user's favorite listing IDs only
  fastify.get('/listing-ids', {
    preHandler: [authHook],
    schema: {
      description: 'Get current user favorite listing IDs only',
      tags: ['favorites'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'User favorite listing IDs retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                userId: { type: 'string', format: 'uuid' },
                favoriteListingIds: {
                  type: 'array',
                  items: { type: 'string', format: 'uuid' }
                },
                count: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.getUserFavoriteListingIds);

  // Bulk remove from favorites
  fastify.delete('/bulk', {
    preHandler: [authHook],
    schema: {
      description: 'Bulk remove listings from favorites',
      tags: ['favorites'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['listingIds'],
        properties: {
          listingIds: {
            type: 'array',
            items: { type: 'string', format: 'uuid' },
            minItems: 1,
            maxItems: 50
          }
        }
      },
      response: {
        200: {
          description: 'Bulk removal completed',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                results: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      listingId: { type: 'string', format: 'uuid' },
                      success: { type: 'boolean' },
                      error: { type: 'string' }
                    }
                  }
                },
                summary: {
                  type: 'object',
                  properties: {
                    total: { type: 'integer' },
                    successful: { type: 'integer' },
                    failed: { type: 'integer' }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.bulkRemoveFromFavorites);

  // Get favorite statistics (optional admin endpoint)
  fastify.get('/statistics', {
    schema: {
      description: 'Get favorite statistics',
      tags: ['favorites'],
      response: {
        200: {
          description: 'Favorite statistics retrieved successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalFavorites: { type: 'integer' },
                usersWithFavorites: { type: 'integer' },
                favoritedListings: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, favoritesController.getFavoriteStatistics);
};

module.exports = favoritesRoutes;
