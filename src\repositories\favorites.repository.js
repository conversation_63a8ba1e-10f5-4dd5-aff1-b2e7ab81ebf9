const { createBaseRepository } = require('./index');
const { cache } = require('../config/cache.config');
const { logger } = require('../utils/logger');

const createFavoritesRepository = ({ models }) => {
  console.log('🔍 Creating favorites repository with models:', models);
  console.log('🔍 Available models:', Object.keys(models || {}));

  if (!models || !models.Favorites) {
    console.error('❌ Favorites model not found in models:', models);
    throw new Error('Favorites model not available');
  }

  const Favorites = models.Favorites;
  const FavoritesRead = models.FavoritesRead || models.Favorites;
  const Listing = models.Listing;
  const ListingRead = models.ListingRead || models.Listing;
  const User = models.User;
  const UserRead = models.UserRead || models.User;

  console.log('✅ Favorites model found:', Favorites.name);

  const baseRepository = createBaseRepository(Favorites);

  return {
    ...baseRepository,

    async addToFavorites(userId, listingId) {
      try {
        // Check if already exists
        const existing = await this.findByUserAndListing(userId, listingId);
        if (existing) {
          throw new Error('Listing already in favorites');
        }

        // Create favorite
        const favorite = await Favorites.create({
          UserID: userId,
          ListingID: listingId
        });

        // Clear user's favorites cache
        await this.clearUserFavoritesCache(userId);

        logger.info(`Added listing ${listingId} to favorites for user ${userId}`);
        return favorite;

      } catch (error) {
        logger.error('Error adding to favorites:', error);
        throw error;
      }
    },

    async removeFromFavorites(userId, listingId) {
      try {
        const favorite = await this.findByUserAndListing(userId, listingId);
        if (!favorite) {
          throw new Error('Favorite not found');
        }

        await favorite.destroy();

        // Clear user's favorites cache
        await this.clearUserFavoritesCache(userId);

        logger.info(`Removed listing ${listingId} from favorites for user ${userId}`);
        return true;

      } catch (error) {
        logger.error('Error removing from favorites:', error);
        throw error;
      }
    },

    async findByUserAndListing(userId, listingId) {
      try {
        return await FavoritesRead.findOne({
          where: {
            UserID: userId,
            ListingID: listingId
          }
        });
      } catch (error) {
        logger.error('Error finding favorite by user and listing:', error);
        throw error;
      }
    },

    async getUserFavorites(userId, options = {}) {
      const cacheKey = `user:${userId}:favorites`;
      
      try {
        // Try cache first
        const cached = await cache.get(cacheKey);
        if (cached && !options.skipCache) {
          logger.debug(`Cache hit for user favorites: ${userId}`);
          return cached;
        }

        // Fetch from database with listing details
        const favorites = await FavoritesRead.findAll({
          where: { UserID: userId },
          include: [
            {
              model: ListingRead,
              as: 'Listing',
              attributes: [
                'ListingID', 'Title', 'Location', 'PricePerDay', 
                'PricePerWeek', 'PricePerMonth', 'Description',
                'Condition', 'IsAvailable', 'IsActive', 'CreatedAt'
              ]
            }
          ],
          order: [['CreatedAt', 'DESC']],
          ...options
        });

        // Cache the result
        if (favorites) {
          await cache.set(cacheKey, favorites, 1800); // 30 minutes
        }

        return favorites;

      } catch (error) {
        logger.error('Error getting user favorites:', error);
        throw error;
      }
    },

    async getListingFavoriteCount(listingId) {
      const cacheKey = `listing:${listingId}:favorite_count`;
      
      try {
        // Try cache first
        const cached = await cache.get(cacheKey);
        if (cached !== null) {
          logger.debug(`Cache hit for listing favorite count: ${listingId}`);
          return parseInt(cached);
        }

        // Count from database
        const count = await FavoritesRead.count({
          where: { ListingID: listingId }
        });

        // Cache the result
        await cache.set(cacheKey, count, 3600); // 1 hour

        return count;

      } catch (error) {
        logger.error('Error getting listing favorite count:', error);
        throw error;
      }
    },

    async isListingFavorited(userId, listingId) {
      try {
        const favorite = await this.findByUserAndListing(userId, listingId);
        return !!favorite;
      } catch (error) {
        logger.error('Error checking if listing is favorited:', error);
        throw error;
      }
    },

    async getUserFavoriteListingIds(userId) {
      const cacheKey = `user:${userId}:favorite_listing_ids`;
      
      try {
        // Try cache first
        const cached = await cache.get(cacheKey);
        if (cached) {
          logger.debug(`Cache hit for user favorite listing IDs: ${userId}`);
          return cached;
        }

        // Fetch from database
        const favorites = await FavoritesRead.findAll({
          where: { UserID: userId },
          attributes: ['ListingID']
        });

        const listingIds = favorites.map(fav => fav.ListingID);

        // Cache the result
        await cache.set(cacheKey, listingIds, 1800); // 30 minutes

        return listingIds;

      } catch (error) {
        logger.error('Error getting user favorite listing IDs:', error);
        throw error;
      }
    },

    async clearUserFavoritesCache(userId) {
      try {
        await Promise.all([
          cache.delete(`user:${userId}:favorites`),
          cache.delete(`user:${userId}:favorite_listing_ids`)
        ]);
        logger.debug(`Cleared favorites cache for user: ${userId}`);
      } catch (error) {
        logger.warn('Error clearing favorites cache:', error);
      }
    },

    async clearListingFavoriteCache(listingId) {
      try {
        await cache.delete(`listing:${listingId}:favorite_count`);
        logger.debug(`Cleared favorite count cache for listing: ${listingId}`);
      } catch (error) {
        logger.warn('Error clearing listing favorite cache:', error);
      }
    },

    async getFavoriteStatistics() {
      try {
        const { Op } = require('sequelize');
        const { masterDb } = require('../config/database.config');

        const stats = await FavoritesRead.findAll({
          attributes: [
            [masterDb.fn('COUNT', masterDb.col('FavoriteID')), 'totalFavorites'],
            [masterDb.fn('COUNT', masterDb.fn('DISTINCT', masterDb.col('UserID'))), 'usersWithFavorites'],
            [masterDb.fn('COUNT', masterDb.fn('DISTINCT', masterDb.col('ListingID'))), 'favoritedListings']
          ],
          raw: true
        });

        return stats[0];

      } catch (error) {
        logger.error('Error getting favorite statistics:', error);
        throw error;
      }
    }
  };
};

module.exports = { createFavoritesRepository };
