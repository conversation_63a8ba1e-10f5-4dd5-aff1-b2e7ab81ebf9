const Joi = require('joi');

const favoritesSchemas = {
  // Add to favorites validation
  addToFavorites: Joi.object({
    listingId: Joi.string().uuid().required()
  }),

  // Remove from favorites validation
  removeFromFavorites: Joi.object({
    listingId: Joi.string().uuid().required()
  }),

  // Get user favorites validation
  getUserFavorites: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    skipCache: Joi.boolean().default(false)
  }),

  // Toggle favorite validation
  toggleFavorite: Joi.object({
    listingId: Joi.string().uuid().required()
  }),

  // Check favorite status validation
  checkFavoriteStatus: Joi.object({
    listingId: Joi.string().uuid().required()
  }),

  // Get listing favorite count validation
  getListingFavoriteCount: Joi.object({
    listingId: Joi.string().uuid().required()
  }),

  // Bulk remove from favorites validation
  bulkRemoveFromFavorites: Joi.object({
    listingIds: Joi.array()
      .items(Joi.string().uuid())
      .min(1)
      .max(50)
      .required()
  }),

  // Favorite response schema
  favoriteResponse: Joi.object({
    FavoriteID: Joi.string().uuid(),
    UserID: Joi.string().uuid(),
    ListingID: Joi.string().uuid(),
    CreatedAt: Joi.date(),
    UpdatedAt: Joi.date(),
    Listing: Joi.object({
      ListingID: Joi.string().uuid(),
      Title: Joi.string(),
      Location: Joi.string(),
      PricePerDay: Joi.number().allow(null),
      PricePerWeek: Joi.number().allow(null),
      PricePerMonth: Joi.number().allow(null),
      Description: Joi.string(),
      Condition: Joi.string().valid('new', 'good', 'used'),
      IsAvailable: Joi.boolean(),
      IsActive: Joi.boolean(),
      CreatedAt: Joi.date()
    }).optional()
  }),

  // Pagination schema
  pagination: Joi.object({
    page: Joi.number().integer().min(1),
    limit: Joi.number().integer().min(1).max(100),
    total: Joi.number().integer().min(0),
    totalPages: Joi.number().integer().min(0)
  }),

  // API response schemas
  successResponse: Joi.object({
    success: Joi.boolean().valid(true),
    data: Joi.any(),
    message: Joi.string(),
    pagination: Joi.object().optional()
  }),

  errorResponse: Joi.object({
    success: Joi.boolean().valid(false),
    error: Joi.string(),
    message: Joi.string(),
    statusCode: Joi.number().integer()
  })
};

module.exports = favoritesSchemas;
