const { publishToRabbit } = require('../config/queue.config');
const { logger } = require('../utils/logger');

const createFavoritesService = ({ favoritesRepository, listingRepository, userRepository }) => {
  return {
    async addToFavorites(userId, listingId) {
      try {
        // Validate user exists
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        // Validate listing exists and is active
        const listing = await listingRepository.findById(listingId);
        if (!listing) {
          throw new Error('Listing not found');
        }

        if (!listing.IsActive || !listing.IsAvailable) {
          throw new Error('Listing is not available for favorites');
        }

        // Check if user is trying to favorite their own listing
        if (listing.ListerID === userId) {
          throw new Error('Cannot add your own listing to favorites');
        }

        // Add to favorites
        const favorite = await favoritesRepository.addToFavorites(userId, listingId);

        // Publish event
        await publishToRabbit('events', 'favorite.added', {
          userId,
          listingId,
          favoriteId: favorite.FavoriteID,
          timestamp: new Date()
        });

        logger.info(`User ${userId} added listing ${listingId} to favorites`);
        return favorite;

      } catch (error) {
        logger.error('Error adding to favorites:', error);
        throw error;
      }
    },

    async removeFromFavorites(userId, listingId) {
      try {
        // Validate user exists
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        // Remove from favorites
        const result = await favoritesRepository.removeFromFavorites(userId, listingId);

        // Publish event
        await publishToRabbit('events', 'favorite.removed', {
          userId,
          listingId,
          timestamp: new Date()
        });

        logger.info(`User ${userId} removed listing ${listingId} from favorites`);
        return result;

      } catch (error) {
        logger.error('Error removing from favorites:', error);
        throw error;
      }
    },

    async getUserFavorites(userId, options = {}) {
      try {
        // Validate user exists
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        const { page = 1, limit = 20, skipCache = false } = options;

        // Calculate offset for pagination
        const offset = (page - 1) * limit;

        // Get favorites with pagination
        const favorites = await favoritesRepository.getUserFavorites(userId, {
          limit,
          offset,
          skipCache
        });

        // Get total count for pagination
        const totalCount = await favoritesRepository.count({ UserID: userId });

        return {
          favorites,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages: Math.ceil(totalCount / limit)
          }
        };

      } catch (error) {
        logger.error('Error getting user favorites:', error);
        throw error;
      }
    },

    async getListingFavoriteCount(listingId) {
      try {
        // Validate listing exists
        const listing = await listingRepository.findById(listingId);
        if (!listing) {
          throw new Error('Listing not found');
        }

        const count = await favoritesRepository.getListingFavoriteCount(listingId);
        return count;

      } catch (error) {
        logger.error('Error getting listing favorite count:', error);
        throw error;
      }
    },

    async isListingFavorited(userId, listingId) {
      try {
        // Validate user exists
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        // Validate listing exists
        const listing = await listingRepository.findById(listingId);
        if (!listing) {
          throw new Error('Listing not found');
        }

        const isFavorited = await favoritesRepository.isListingFavorited(userId, listingId);
        return isFavorited;

      } catch (error) {
        logger.error('Error checking if listing is favorited:', error);
        throw error;
      }
    },

    async toggleFavorite(userId, listingId) {
      try {
        const isFavorited = await this.isListingFavorited(userId, listingId);

        if (isFavorited) {
          await this.removeFromFavorites(userId, listingId);
          return { action: 'removed', isFavorited: false };
        } else {
          await this.addToFavorites(userId, listingId);
          return { action: 'added', isFavorited: true };
        }

      } catch (error) {
        logger.error('Error toggling favorite:', error);
        throw error;
      }
    },

    async getUserFavoriteListingIds(userId) {
      try {
        // Validate user exists
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        const listingIds = await favoritesRepository.getUserFavoriteListingIds(userId);
        return listingIds;

      } catch (error) {
        logger.error('Error getting user favorite listing IDs:', error);
        throw error;
      }
    },

    async getFavoriteStatistics() {
      try {
        const stats = await favoritesRepository.getFavoriteStatistics();
        return stats;

      } catch (error) {
        logger.error('Error getting favorite statistics:', error);
        throw error;
      }
    },

    async clearUserFavoritesCache(userId) {
      try {
        await favoritesRepository.clearUserFavoritesCache(userId);
        logger.info(`Cleared favorites cache for user: ${userId}`);

      } catch (error) {
        logger.error('Error clearing user favorites cache:', error);
        throw error;
      }
    },

    async bulkRemoveFromFavorites(userId, listingIds) {
      try {
        // Validate user exists
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        const results = [];
        for (const listingId of listingIds) {
          try {
            await this.removeFromFavorites(userId, listingId);
            results.push({ listingId, success: true });
          } catch (error) {
            results.push({ listingId, success: false, error: error.message });
          }
        }

        return results;

      } catch (error) {
        logger.error('Error bulk removing from favorites:', error);
        throw error;
      }
    }
  };
};

module.exports = { createFavoritesService };
