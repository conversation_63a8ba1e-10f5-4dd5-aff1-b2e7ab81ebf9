const { logger } = require('../utils/logger');

const createFavoritesMediator = ({ favoritesService, validator }) => {
  return {
    async addToFavorites(request) {
      try {
        const { listingId } = request.params;
        const userId = request.user?.id;

        if (!userId) {
          throw new Error('User authentication required');
        }

        // Validate listing ID format
        if (!validator.isUUID(listingId)) {
          throw new Error('Invalid listing ID format');
        }

        // Call service
        const favorite = await favoritesService.addToFavorites(userId, listingId);

        // Format response
        return {
          success: true,
          data: {
            favoriteId: favorite.FavoriteID,
            userId: favorite.UserID,
            listingId: favorite.ListingID,
            createdAt: favorite.CreatedAt
          },
          message: 'Listing added to favorites successfully'
        };

      } catch (error) {
        logger.error(`Add to favorites failed: ${error.message}`);
        throw error;
      }
    },

    async removeFromFavorites(request) {
      try {
        const { listingId } = request.params;
        const userId = request.user?.id;

        if (!userId) {
          throw new Error('User authentication required');
        }

        // Validate listing ID format
        if (!validator.isUUID(listingId)) {
          throw new Error('Invalid listing ID format');
        }

        // Call service
        await favoritesService.removeFromFavorites(userId, listingId);

        // Format response
        return {
          success: true,
          message: 'Listing removed from favorites successfully'
        };

      } catch (error) {
        logger.error(`Remove from favorites failed: ${error.message}`);
        throw error;
      }
    },

    async getUserFavorites(request) {
      try {
        const userId = request.user?.id;
        const { page = 1, limit = 20, skipCache = false } = request.query;

        if (!userId) {
          throw new Error('User authentication required');
        }

        // Validate pagination parameters
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);

        if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
          throw new Error('Invalid pagination parameters');
        }

        // Call service
        const result = await favoritesService.getUserFavorites(userId, {
          page: pageNum,
          limit: limitNum,
          skipCache: skipCache === 'true'
        });

        // Format response
        return {
          success: true,
          data: result.favorites,
          pagination: result.pagination,
          message: 'User favorites retrieved successfully'
        };

      } catch (error) {
        logger.error(`Get user favorites failed: ${error.message}`);
        throw error;
      }
    },

    async getListingFavoriteCount(request) {
      try {
        const { listingId } = request.params;

        // Validate listing ID format
        if (!validator.isUUID(listingId)) {
          throw new Error('Invalid listing ID format');
        }

        // Call service
        const count = await favoritesService.getListingFavoriteCount(listingId);

        // Format response
        return {
          success: true,
          data: {
            listingId,
            favoriteCount: count
          },
          message: 'Listing favorite count retrieved successfully'
        };

      } catch (error) {
        logger.error(`Get listing favorite count failed: ${error.message}`);
        throw error;
      }
    },

    async isListingFavorited(request) {
      try {
        const { listingId } = request.params;
        const userId = request.user?.id;

        if (!userId) {
          throw new Error('User authentication required');
        }

        // Validate listing ID format
        if (!validator.isUUID(listingId)) {
          throw new Error('Invalid listing ID format');
        }

        // Call service
        const isFavorited = await favoritesService.isListingFavorited(userId, listingId);

        // Format response
        return {
          success: true,
          data: {
            listingId,
            isFavorited
          },
          message: 'Favorite status retrieved successfully'
        };

      } catch (error) {
        logger.error(`Check favorite status failed: ${error.message}`);
        throw error;
      }
    },

    async toggleFavorite(request) {
      try {
        const { listingId } = request.params;
        const userId = request.user?.id;

        if (!userId) {
          throw new Error('User authentication required');
        }

        // Validate listing ID format
        if (!validator.isUUID(listingId)) {
          throw new Error('Invalid listing ID format');
        }

        // Call service
        const result = await favoritesService.toggleFavorite(userId, listingId);

        // Format response
        return {
          success: true,
          data: {
            listingId,
            action: result.action,
            isFavorited: result.isFavorited
          },
          message: `Listing ${result.action} ${result.action === 'added' ? 'to' : 'from'} favorites successfully`
        };

      } catch (error) {
        logger.error(`Toggle favorite failed: ${error.message}`);
        throw error;
      }
    },

    async getUserFavoriteListingIds(request) {
      try {
        const userId = request.user?.id;

        if (!userId) {
          throw new Error('User authentication required');
        }

        // Call service
        const listingIds = await favoritesService.getUserFavoriteListingIds(userId);

        // Format response
        return {
          success: true,
          data: {
            userId,
            favoriteListingIds: listingIds,
            count: listingIds.length
          },
          message: 'User favorite listing IDs retrieved successfully'
        };

      } catch (error) {
        logger.error(`Get user favorite listing IDs failed: ${error.message}`);
        throw error;
      }
    },

    async getFavoriteStatistics(request) {
      try {
        // Check if user has admin role (optional - you can remove this if not needed)
        // if (request.user?.role !== 'admin') {
        //   throw new Error('Admin access required');
        // }

        // Call service
        const stats = await favoritesService.getFavoriteStatistics();

        // Format response
        return {
          success: true,
          data: stats,
          message: 'Favorite statistics retrieved successfully'
        };

      } catch (error) {
        logger.error(`Get favorite statistics failed: ${error.message}`);
        throw error;
      }
    },

    async bulkRemoveFromFavorites(request) {
      try {
        const userId = request.user?.id;
        const { listingIds } = request.body;

        if (!userId) {
          throw new Error('User authentication required');
        }

        // Validate request body
        if (!Array.isArray(listingIds) || listingIds.length === 0) {
          throw new Error('Invalid listing IDs array');
        }

        // Validate each listing ID
        for (const listingId of listingIds) {
          if (!validator.isUUID(listingId)) {
            throw new Error(`Invalid listing ID format: ${listingId}`);
          }
        }

        // Call service
        const results = await favoritesService.bulkRemoveFromFavorites(userId, listingIds);

        // Count successes and failures
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;

        // Format response
        return {
          success: true,
          data: {
            results,
            summary: {
              total: results.length,
              successful: successCount,
              failed: failureCount
            }
          },
          message: `Bulk removal completed: ${successCount} successful, ${failureCount} failed`
        };

      } catch (error) {
        logger.error(`Bulk remove from favorites failed: ${error.message}`);
        throw error;
      }
    }
  };
};

module.exports = { createFavoritesMediator };
