const { logger } = require('../../../utils/logger');

const favoritesController = {
  async addToFavorites(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.addToFavorites(request);
      reply.code(201).send(result);

    } catch (error) {
      logger.error(`Failed to add to favorites: ${error.message}`);
      throw error;
    }
  },

  async removeFromFavorites(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.removeFromFavorites(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to remove from favorites: ${error.message}`);
      throw error;
    }
  },

  async getUserFavorites(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.getUserFavorites(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to get user favorites: ${error.message}`);
      throw error;
    }
  },

  async getListingFavoriteCount(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.getListingFavoriteCount(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to get listing favorite count: ${error.message}`);
      throw error;
    }
  },

  async isListingFavorited(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.isListingFavorited(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to check favorite status: ${error.message}`);
      throw error;
    }
  },

  async toggleFavorite(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.toggleFavorite(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to toggle favorite: ${error.message}`);
      throw error;
    }
  },

  async getUserFavoriteListingIds(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.getUserFavoriteListingIds(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to get user favorite listing IDs: ${error.message}`);
      throw error;
    }
  },

  async getFavoriteStatistics(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.getFavoriteStatistics(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to get favorite statistics: ${error.message}`);
      throw error;
    }
  },

  async bulkRemoveFromFavorites(request, reply) {
    try {
      const container = request.server.container;
      const favoritesMediator = container.resolve('favoritesMediator');
      
      const result = await favoritesMediator.bulkRemoveFromFavorites(request);
      reply.send(result);

    } catch (error) {
      logger.error(`Failed to bulk remove from favorites: ${error.message}`);
      throw error;
    }
  }
};

module.exports = favoritesController;
