const { DataTypes } = require('sequelize');
const bcrypt = require('bcrypt');

const defineUserModel = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fullName: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(150),
      allowNull: false,
      unique: true,
      validate: { isEmail: true }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      unique: true
    },
    cnic: {
      type: DataTypes.STRING(15),
      allowNull: true,
      unique: true
    },
    ProfileImageURL: {
      type: DataTypes.STRING,
      allowNull: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    // lastPasswordChange: {
    //   type: DataTypes.DATE,
    //   allowNull: true
    // }
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'CreatedAt',
    updatedAt: 'UpdatedAt',
    defaultScope: {
      where: { isDeleted: false }
    },
    scopes: {
      withDeleted: {
        where: {}
      }
    },
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 10);
          user.lastPasswordChange = new Date(); // ✅ update on password change
        }
      }
    }
  });

  // Instance Methods
  User.prototype.comparePassword = async function (inputPassword) {
    if (!inputPassword || !this.password) {
      throw new Error("Password data is missing");
    }
    return await bcrypt.compare(inputPassword, this.password);
  };

  User.prototype.toJSON = function () {
    const values = { ...this.get() };
    delete values.password;
    return values;
  };

  // Static Methods
  User.findByEmail = async function (email) {
    return this.findOne({ where: { email } });
  };

  User.findByUsername = async function (username) {
    return this.findOne({ where: { username } });
  };

  User.findActive = async function () {
    return this.findAll({ where: { status: 'active' } });
  };

  // Associations
  User.associate = (models) => {
    if (models.Listing) {
      User.hasMany(models.Listing, {
        foreignKey: 'ListerID',
        as: 'Listings',
        onDelete: 'CASCADE'
      });
    }

    if (models.Favorites) {
      User.hasMany(models.Favorites, {
        foreignKey: 'UserID',
        as: 'Favorites',
        onDelete: 'CASCADE'
      });
    }

    // Other associations (e.g., Bookings, Messages, etc.) can be added similarly
  };

  return User;
};

module.exports = defineUserModel;
