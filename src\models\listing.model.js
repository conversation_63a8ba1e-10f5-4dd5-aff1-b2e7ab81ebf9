const { DataTypes } = require('sequelize');

const defineListingModel = (sequelize) => {
  const Listing = sequelize.define('Listing', {
    ListingID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    ListerID: {
      type: DataTypes.UUID,
      allowNull: false,
      // defaultValue: 'test-user-id'
      references: {
        model: 'users',
        key: 'id'
      }
    },
    Title: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    CategoryID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'categories',
        key: 'CategoryID'
      }
    },
    Location: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
     SubcategoryID: {
       type: DataTypes.UUID,
       allowNull: false,
       references: {
         model: 'subcategories',
         key: 'SubcategoryID'
       }
     },
    PricePerDay: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    PricePerWeek: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    PricePerMonth: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    SecurityDeposit: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    },
    Description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    DeliveryOptions: {
      type: DataTypes.ENUM('pickup', 'homeDelivery'),
      allowNull: false,
      defaultValue: 'pickup',
    },
    Condition: {
      type: DataTypes.ENUM('new', 'good', 'used'),
      allowNull: false,
      defaultValue: 'new',
    },

    Latitude: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true
    },
    Longitude: {
      type: DataTypes.DECIMAL(11, 8),
      allowNull: true
    },


    IsAvailable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    ViewCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    FeaturedUntil: {
      type: DataTypes.DATE,
      allowNull: true
    },
    CreatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'listings',
    timestamps: false,
    indexes: [
      { fields: ['CategoryID'] },
      { fields: ['SubcategoryID'] },
      { fields: ['ListerID'] },
      { fields: ['Location'] },
      { fields: ['PricePerDay'] },
      { fields: ['IsAvailable'] },
      { fields: ['IsActive'] },
      { fields: ['CreatedAt'] },
      { fields: ['FeaturedUntil'] }
    ],
    hooks: {
      beforeUpdate: (listing) => {
        listing.UpdatedAt = new Date();
      }
    }
  });

  Listing.associate = (models) => {
    // User who listed the item
    Listing.belongsTo(models.User, {
      foreignKey: 'ListerID',
      as: 'Lister'
    });

    // Category
    Listing.belongsTo(models.Category, {
      foreignKey: 'CategoryID',
      as: 'Category'
    });

    // Subcategory
    Listing.belongsTo(models.Subcategory, {
      foreignKey: 'SubcategoryID',
      as: 'Subcategory'
    });

    // TODO: Uncomment these associations once the models are created

    // Specialized listing type associations
    Listing.hasOne(models.Property, {
      foreignKey: 'ListingID',
      as: 'PropertyDetails'
    });

    Listing.hasOne(models.Vehicle, {
      foreignKey: 'ListingID',
      as: 'VehicleDetails'
    });

    Listing.hasOne(models.Furniture, {
      foreignKey: 'ListingID',
      as: 'FurnitureDetails'
    });

    Listing.hasOne(models.HomeAppliance, {
      foreignKey: 'ListingID',
      as: 'HomeApplianceDetails'
    });

    Listing.hasOne(models.PhotographyEquipment, {
      foreignKey: 'ListingID',
      as: 'PhotographyEquipmentDetails'
    });

    // Favorites association
    Listing.hasMany(models.Favorites, {
      foreignKey: 'ListingID',
      as: 'Favorites',
      onDelete: 'CASCADE'
    });

  };


  return Listing;
};

module.exports = defineListingModel;
